"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateMetrics = void 0;
const CAPTURE_INTERVAL = 20; // 20 seconds
const calculateMetrics = (sessions) => {
    if (sessions.length === 0) {
        return { totalStudyTime: 0, focusedTime: 0, focusRate: 0, totalBreakTime: 0, breakTimePercentage: 0, breakCount: 0, medianBreakInterval: 0 };
    }
    let totalStudyTime = 0;
    let focusedTime = 0;
    let totalBreakTime = 0;
    let breakCount = 0;
    const studyIntervals = [];
    sessions.forEach(session => {
        if (!session.endTime)
            return;
        const sessionEndTime = new Date(session.endTime).getTime();
        const sessionStartTime = new Date(session.startTime).getTime();
        const sessionDuration = (sessionEndTime - sessionStartTime) / 1000;
        const sessionBreakTime = session.breakHistory.reduce((acc, br) => {
            const breakEndTime = br.endTime ? new Date(br.endTime).getTime() : new Date().getTime();
            const breakStartTime = new Date(br.startTime).getTime();
            return acc + (breakEndTime - breakStartTime) / 1000;
        }, 0);
        totalStudyTime += (sessionDuration - sessionBreakTime);
        totalBreakTime += sessionBreakTime;
        breakCount += session.breakHistory.length;
        focusedTime += session.focusHistory.filter(f => f.isFocused).length * CAPTURE_INTERVAL;
        let lastEventTime = sessionStartTime;
        session.breakHistory.forEach(br => {
            if (br.endTime) {
                studyIntervals.push((new Date(br.startTime).getTime() - lastEventTime) / 1000);
                lastEventTime = new Date(br.endTime).getTime();
            }
        });
        studyIntervals.push((sessionEndTime - lastEventTime) / 1000);
    });
    const focusRate = totalStudyTime > 0 ? (focusedTime / totalStudyTime) * 100 : 0;
    const grandTotalTime = totalStudyTime + totalBreakTime;
    const breakTimePercentage = grandTotalTime > 0 ? (totalBreakTime / grandTotalTime) * 100 : 0;
    let medianBreakInterval = 0;
    if (studyIntervals.length > 0) {
        const sortedIntervals = [...studyIntervals].sort((a, b) => a - b);
        const mid = Math.floor(sortedIntervals.length / 2);
        medianBreakInterval = sortedIntervals.length % 2 !== 0 ? sortedIntervals[mid] : (sortedIntervals[mid - 1] + sortedIntervals[mid]) / 2;
    }
    return {
        totalStudyTime,
        focusedTime,
        focusRate: Math.min(100, focusRate),
        totalBreakTime,
        breakTimePercentage,
        breakCount,
        medianBreakInterval
    };
};
exports.calculateMetrics = calculateMetrics;
