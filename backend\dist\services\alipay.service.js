"use strict";
// NOTE: This is a MOCK implementation for demonstration purposes.
// A real Alipay integration requires using their official SDK (e.g., alipay-sdk-nodejs-ts)
// and handling cryptography (signing and verification) correctly.
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyAlipaySign = exports.generateAlipayUrl = void 0;
const types_1 = require("../types");
const config_1 = __importDefault(require("../config"));
/**
 * MOCK: Generates a fake Alipay payment URL.
 * In a real app, this would use the Alipay SDK to create a real order.
 */
const generateAlipayUrl = async (userId, plan) => {
    const planInfo = types_1.PLAN_DETAILS[plan];
    if (!planInfo) {
        throw new Error('Invalid plan specified');
    }
    // This is our unique order ID. We embed user and plan info for the webhook.
    const outTradeNo = `sub-${plan}-${userId}-${Date.now()}`;
    const totalAmount = planInfo.price.toFixed(2);
    const subject = `AI 作业监督员 - ${planInfo.name}`;
    // This URL simulates the redirect to Alipay.
    // The query params mimic what the Alipay gateway would receive.
    const mockGatewayParams = new URLSearchParams({
        app_id: config_1.default.alipay.appId,
        method: 'alipay.trade.page.pay',
        out_trade_no: outTradeNo,
        total_amount: totalAmount,
        subject: subject,
        product_code: 'FAST_INSTANT_TRADE_PAY',
        // In a real scenario, a signature would be generated and included.
        sign: 'MOCK_SIGNATURE_VALUE',
    });
    const paymentUrl = `${config_1.default.alipay.gatewayUrl}?${mockGatewayParams.toString()}`;
    // For local testing, we also create a webhook callback URL that the developer can manually trigger.
    const mockCallbackUrl = `http://localhost:${config_1.default.port}/api/subscription/webhook/alipay`;
    console.log('--- MOCK ALIPAY PAYMENT ---');
    console.log(`Payment URL generated: ${paymentUrl}`);
    console.log(`To simulate successful payment, send a POST request to ${mockCallbackUrl} with the following body:`);
    console.log(JSON.stringify({
        out_trade_no: outTradeNo,
        trade_no: `ALIPAY_MOCK_${Date.now()}`,
        trade_status: 'TRADE_SUCCESS',
        // In a real scenario, many more parameters are included.
    }, null, 2));
    console.log('---------------------------');
    return { paymentUrl, outTradeNo };
};
exports.generateAlipayUrl = generateAlipayUrl;
/**
 * MOCK: Verifies an incoming webhook request.
 * In a real app, this would use the Alipay SDK's `checkSign` method
 * with the public key from the Alipay developer portal.
 */
const verifyAlipaySign = (params) => {
    // For this mock, we'll just assume the request is valid if it has our order ID.
    console.log('Verifying Alipay signature (MOCK)...');
    if (params && params.out_trade_no) {
        console.log('Signature mock verification PASSED.');
        return true;
    }
    console.log('Signature mock verification FAILED.');
    return false;
};
exports.verifyAlipaySign = verifyAlipaySign;
