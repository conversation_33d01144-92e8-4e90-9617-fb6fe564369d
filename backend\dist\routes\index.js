"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_routes_1 = __importDefault(require("./auth.routes"));
const user_routes_1 = __importDefault(require("./user.routes"));
const supervision_routes_1 = __importDefault(require("./supervision.routes"));
const subscription_routes_1 = __importDefault(require("./subscription.routes"));
const report_routes_1 = __importDefault(require("./report.routes"));
const session_routes_1 = __importDefault(require("./session.routes"));
const router = (0, express_1.Router)();
router.use('/auth', auth_routes_1.default);
router.use('/user', user_routes_1.default);
router.use('/supervision', supervision_routes_1.default);
router.use('/subscription', subscription_routes_1.default);
router.use('/reports', report_routes_1.default);
router.use('/sessions', session_routes_1.default);
exports.default = router;
