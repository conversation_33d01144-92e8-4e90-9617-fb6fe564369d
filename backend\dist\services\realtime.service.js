"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendSessionUpdateToUser = exports.initRealtimeService = void 0;
const http_1 = require("http");
const ws_1 = require("ws");
const url_1 = __importDefault(require("url"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = __importDefault(require("../config"));
const studySession_model_1 = __importStar(require("../models/studySession.model"));
const userSockets = new Map();
const INACTIVITY_TIMEOUT_MS = 5 * 60 * 1000; // 5 minutes
const initRealtimeService = (app) => {
    const server = (0, http_1.createServer)(app);
    const wss = new ws_1.WebSocketServer({ server });
    wss.on('connection', (ws, req) => {
        const token = url_1.default.parse(req.url, true).query.token;
        if (!token) {
            ws.terminate();
            return;
        }
        try {
            const decoded = jsonwebtoken_1.default.verify(token, config_1.default.jwtSecret);
            const userId = decoded.id;
            if (!userSockets.has(userId)) {
                userSockets.set(userId, []);
            }
            userSockets.get(userId).push(ws);
            console.log(`WebSocket connected for user: ${userId}`);
            ws.on('close', () => {
                const sockets = userSockets.get(userId) || [];
                const index = sockets.indexOf(ws);
                if (index > -1) {
                    sockets.splice(index, 1);
                }
                if (sockets.length === 0) {
                    userSockets.delete(userId);
                }
                console.log(`WebSocket disconnected for user: ${userId}`);
            });
            ws.on('error', (error) => {
                console.error(`WebSocket error for user ${userId}:`, error);
            });
        }
        catch (error) {
            console.error('WebSocket connection failed: Invalid token');
            ws.terminate();
        }
    });
    // Start background job to clean up stale sessions
    setInterval(checkStaleSessions, 60 * 1000); // Check every minute
    console.log('Stale session checker started.');
    return server;
};
exports.initRealtimeService = initRealtimeService;
const sendSessionUpdateToUser = (userId, session) => {
    const sockets = userSockets.get(userId);
    if (sockets) {
        const payload = JSON.stringify(session);
        sockets.forEach(ws => {
            if (ws.readyState === ws_1.WebSocket.OPEN) {
                ws.send(payload);
            }
        });
    }
};
exports.sendSessionUpdateToUser = sendSessionUpdateToUser;
const checkStaleSessions = async () => {
    const timeoutThreshold = new Date(Date.now() - INACTIVITY_TIMEOUT_MS);
    try {
        const staleSessions = await studySession_model_1.default.find({
            status: { $in: [studySession_model_1.StudyStatus.Studying, studySession_model_1.StudyStatus.Break] },
            lastActivity: { $lt: timeoutThreshold },
        });
        if (staleSessions.length > 0) {
            console.log(`Found ${staleSessions.length} stale session(s) to terminate.`);
        }
        for (const session of staleSessions) {
            const lastActivityTime = session.lastActivity || new Date(); // Fallback
            session.status = studySession_model_1.StudyStatus.Finished;
            session.endTime = lastActivityTime;
            // Close any open breaks
            const openBreak = session.breakHistory.find(b => !b.endTime);
            if (openBreak) {
                openBreak.endTime = lastActivityTime;
            }
            await session.save();
            console.log(`Terminated stale session ${session._id} for user ${session.user}`);
            // Notify user if they are still somehow connected
            (0, exports.sendSessionUpdateToUser)(session.user.toString(), session);
        }
    }
    catch (error) {
        console.error('Error checking for stale sessions:', error);
    }
};
