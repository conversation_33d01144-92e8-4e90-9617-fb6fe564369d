"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.analyzeImageWithAI = void 0;
const axios_1 = __importDefault(require("axios"));
const config_1 = __importDefault(require("../config"));
const studySession_model_1 = require("../models/studySession.model");
const getAnimationResponse = (rank, isFocused, isOnSeat) => {
    const pickRandom = (arr) => arr[Math.floor(Math.random() * arr.length)];
    // Default messages for a focused Wukong
    if (isOnSeat && isFocused) {
        return {
            animationKey: 'idle',
            message: pickRandom([
                `善哉善哉，孺子可教也。如此专注，离修成正果又近了一步。`,
                `不错，你潜心修炼的样子，颇有为师当年的风范。`,
                `很好，气息沉稳，心无旁骛。待你学成，三界之内，任你驰骋。`
            ])
        };
    }
    // If not focused, tailor message to the current rank
    if (isOnSeat && !isFocused) {
        switch (rank) {
            case studySession_model_1.Rank.BAJIE:
                return { animationKey: 'distracted', message: pickRandom([
                        "徒儿，你此刻的专注力有些像八戒哦，是不是想偷懒了？快把心神收回来！",
                        "八戒，高老庄的饭好吃，还是师父的经好听？你的心思都飘到哪儿去啦！",
                        "净坛使者（八戒），莫流口水，眼前的知识可比馒头香！"
                    ]) };
            default: // Wukong being distracted
                return { animationKey: 'distracted', message: pickRandom([
                        `悟空，为师掐指一算，你此刻心有旁骛，莫非是想起了花果山的桃子？`,
                        `徒儿，修行之路，贵在专心。你这东张西望的，是想学个“千里眼”的神通吗？`,
                        `咳咳... 悟空，莫要发散心神，那作业里的妖魔鬼怪，可比你想象的厉害。`
                    ]) };
        }
    }
    // If off seat, tailor message to the current rank
    if (!isOnSeat) {
        switch (rank) {
            case studySession_model_1.Rank.SHASENG:
                return { animationKey: 'off_seat', message: pickRandom([
                        "悟净，行李（学习）都不要了？快回来，莫误了取经（学习）的大事！",
                        "沙师弟，你最是稳重，怎能轻易离开岗位？快回到你的宝座上来。"
                    ]) };
            case studySession_model_1.Rank.BAILONGMA:
                return { animationKey: 'off_seat', message: pickRandom([
                        "小白龙，你这脚力，是想一步跑到西天吗？快回到座位上，一步一个脚印才是修行。",
                        "小马儿，别乱跑，你驮着的是为师的期望（和你的作业）！"
                    ]) };
            default: // Wukong off seat
                return { animationKey: 'off_seat', message: pickRandom([
                        `悟空，你的莲花宝座（座位）都快凉了，修行可不能半途而废啊。`,
                        `徒儿，人虽离席，道心勿远。快回来，莫让心猿意马，跑得太远。`
                    ]) };
        }
    }
    return { animationKey: 'idle', message: '保持住，修行之路，就在脚下。' }; // Fallback
};
const analyzeImageWithAI = async (base64Image, profile, currentRank, isRankFinal = false) => {
    const prompt = `分析图像中的孩子。他/她是否专注（例如，看书、写字）并坐在座位上？请仅返回一个JSON对象，包含 'isFocused' (boolean) 和 'isOnSeat' (boolean) 两个键。`;
    let partialAnalysis;
    let usageData;
    // This check prevents making a second API call just to get a new message
    if (isRankFinal) {
        // This is a simplified call, in a real scenario you would pass the analysis result
        // from the previous step instead of assuming it. For this implementation, we assume
        // the image state hasn't changed drastically in the milliseconds between calls.
        partialAnalysis = { isFocused: true, isOnSeat: true }; // Placeholder
        usageData = { input_tokens: 0, output_tokens: 0 };
    }
    else {
        const requestBody = {
            model: "qwen-vl-plus",
            input: {
                messages: [
                    { role: "user", content: [{ image: base64Image }, { text: prompt }] }
                ]
            },
            parameters: { result_format: "json_object" }
        };
        try {
            const response = await axios_1.default.post(config_1.default.qwenApiBaseUrl, requestBody, {
                headers: { 'Authorization': `Bearer ${config_1.default.qwenApiKey}`, 'Content-Type': 'application/json' }
            });
            const outputText = response.data.output.text;
            partialAnalysis = JSON.parse(outputText);
            usageData = response.data.usage;
        }
        catch (error) {
            console.error("Error calling Qwen API:", error.response?.data || error.message);
            throw new Error('AI analysis failed. Please check the backend logs.');
        }
    }
    const { animationKey, message } = getAnimationResponse(currentRank, partialAnalysis.isFocused, partialAnalysis.isOnSeat);
    const analysis = {
        ...partialAnalysis,
        animationKey,
        message,
        currentRank
    };
    const usage = {
        input: usageData.input_tokens || 0,
        output: usageData.output_tokens || 0,
        total: (usageData.input_tokens || 0) + (usageData.output_tokens || 0),
    };
    return { analysis, usage };
};
exports.analyzeImageWithAI = analyzeImageWithAI;
