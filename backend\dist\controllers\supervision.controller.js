"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.analyzeImage = void 0;
const qwen_service_1 = require("../services/qwen.service");
const childProfile_model_1 = __importDefault(require("../models/childProfile.model"));
const studySession_model_1 = __importStar(require("../models/studySession.model"));
const realtime_service_1 = require("../services/realtime.service");
const calculateRank = (focusHistory) => {
    const historySlice = focusHistory.slice(-5); // Analyze the last 5 data points
    if (historySlice.length < 3)
        return studySession_model_1.Rank.WUKONG; // Not enough data to judge
    const offSeatCount = historySlice.filter(h => !h.isOnSeat).length;
    const distractedCount = historySlice.filter(h => !h.isFocused && h.isOnSeat).length;
    if (offSeatCount >= 3)
        return studySession_model_1.Rank.BAILONGMA;
    if (offSeatCount >= 1)
        return studySession_model_1.Rank.SHASENG;
    if (distractedCount >= 3)
        return studySession_model_1.Rank.BAJIE;
    return studySession_model_1.Rank.WUKONG;
};
const analyzeImage = async (req, res, next) => {
    const authReq = req;
    const { base64Image } = req.body;
    if (!base64Image) {
        return res.status(400).json({ message: 'Image data is required.' });
    }
    try {
        const profile = await childProfile_model_1.default.findOne({ user: authReq.user.id });
        if (!profile) {
            return res.status(404).json({ message: 'Child profile not found.' });
        }
        // Find the active (studying or on break) study session
        const activeSession = await studySession_model_1.default.findOne({
            user: authReq.user.id,
            status: { $in: [studySession_model_1.StudyStatus.Studying, studySession_model_1.StudyStatus.Break] }
        });
        if (!activeSession) {
            return res.status(404).json({ message: 'No active session found. Cannot perform analysis.' });
        }
        // Update activity timestamp to prevent timeout
        activeSession.lastActivity = new Date();
        // Only record focus history and update rank if the child is supposed to be studying
        if (activeSession.status === studySession_model_1.StudyStatus.Studying) {
            const currentFocus = await (0, qwen_service_1.analyzeImageWithAI)(base64Image, profile, activeSession.currentRank);
            activeSession.focusHistory.push({
                timestamp: new Date(),
                isFocused: currentFocus.analysis.isFocused,
                isOnSeat: currentFocus.analysis.isOnSeat,
            });
            // Recalculate and update rank based on recent history
            const newRank = calculateRank(activeSession.focusHistory);
            activeSession.currentRank = newRank;
            await activeSession.save();
            // Get the final analysis message based on the *new* rank
            const finalResult = await (0, qwen_service_1.analyzeImageWithAI)(base64Image, profile, newRank, true);
            // Send an immediate session update over WebSocket with the new rank
            (0, realtime_service_1.sendSessionUpdateToUser)(authReq.user.id, activeSession);
            res.json(finalResult);
        }
        else { // If on break, just do a basic analysis to keep the session alive but don't record history
            const breakAnalysis = await (0, qwen_service_1.analyzeImageWithAI)(base64Image, profile, activeSession.currentRank);
            await activeSession.save();
            res.json(breakAnalysis);
        }
    }
    catch (error) {
        next(error);
    }
};
exports.analyzeImage = analyzeImage;
