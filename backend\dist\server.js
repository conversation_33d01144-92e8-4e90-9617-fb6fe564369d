"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const app_1 = __importDefault(require("./app"));
const config_1 = __importDefault(require("./config"));
const db_1 = __importDefault(require("./config/db"));
const realtime_service_1 = require("./services/realtime.service");
const PORT = config_1.default.port || 5000;
// Connect to database
(0, db_1.default)();
// Create an HTTP server from the Express app and initialize WebSocket service
const server = (0, realtime_service_1.initRealtimeService)(app_1.default);
server.listen(PORT, () => {
    console.log(`Backend server is running on http://localhost:${PORT}`);
});
