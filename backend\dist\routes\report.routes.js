"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const report_controller_1 = require("../controllers/report.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const router = (0, express_1.Router)();
// e.g., /api/reports/daily, /api/reports/weekly
router.get('/:period', auth_middleware_1.protect, report_controller_1.getReport);
exports.default = router;
