
import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import apiRoutes from './routes';
import { errorMiddleware } from './middleware/error.middleware';
import config from './config';

dotenv.config();

const app = express();

// Middleware
app.use(cors({
  origin: config.frontendUrl
}));
app.use(express.json({ limit: '10mb' }) as any); // Increase limit for base64 images
app.use(express.urlencoded({ extended: true }) as any);

// API Routes
app.get('/', (req, res) => {
    res.send('AI Homework Supervisor Backend is running.');
});
app.use('/api', apiRoutes);

// Error Handling
app.use(errorMiddleware as any);

export default app;
