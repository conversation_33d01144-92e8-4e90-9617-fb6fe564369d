
import { Request, Response, NextFunction } from 'express';
import StudySession, { StudyStatus, Rank } from '../models/studySession.model';
import ChildProfile from '../models/childProfile.model';
import { AuthenticatedRequest } from '../types';
import { sendSessionUpdateToUser } from '../services/realtime.service';
import { calculateMetrics } from '../services/report.service';

const MONKEY_KING_FOCUS_GOAL_SECONDS = 3600; // 1 hour

// Start a new study session
export const startSession = async (req: Request, res: Response, next: NextFunction) => {
    const authReq = req as AuthenticatedRequest;
    const userId = authReq.user!.id;
    try {
        const existingSession = await StudySession.findOne({
            user: authReq.user!.id,
            status: { $in: [StudyStatus.Studying, StudyStatus.Break] },
        });

        if (existingSession) {
            return (res as any).status(400).json({ message: 'An active session already exists.' });
        }
        
        // Reset daily focus seconds if it's a new day
        const profile = await ChildProfile.findOne({ user: userId });
        if (profile) {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            if (profile.lastFocusUpdate < today) {
                profile.dailyFocusSeconds = 0;
            }
            profile.lastFocusUpdate = new Date();
            await profile.save();
        }

        const now = new Date();
        const newSession = new StudySession({
            user: userId,
            startTime: now,
            status: StudyStatus.Studying,
            lastActivity: now,
            currentRank: Rank.WUKONG, // Initialize rank
        });

        await newSession.save();
        sendSessionUpdateToUser(userId, newSession);
        (res as any).status(201).json(newSession);
    } catch (error) {
        next(error);
    }
};

// Stop the current study session
export const stopSession = async (req: Request, res: Response, next: NextFunction) => {
    const authReq = req as AuthenticatedRequest;
    const userId = authReq.user!.id;
    try {
        const session = await StudySession.findOneAndUpdate(
            { user: userId, status: { $in: [StudyStatus.Studying, StudyStatus.Break] } },
            {
                $set: {
                    status: StudyStatus.Finished,
                    endTime: new Date(),
                    'breakHistory.$[elem].endTime': new Date(), // Close any open breaks
                },
            },
            {
                arrayFilters: [{ 'elem.endTime': { $exists: false } }],
                new: true,
            }
        );

        if (!session) {
            return (res as any).status(404).json({ message: 'No active session found to stop.' });
        }
        
        // Update gamification progress
        const profile = await ChildProfile.findOne({ user: userId });
        if (profile) {
            const metrics = calculateMetrics([session]);
            profile.dailyFocusSeconds += metrics.focusedTime;
            profile.lastFocusUpdate = new Date();

            if (profile.gamificationStage === 'STONE_MONKEY' && profile.dailyFocusSeconds >= MONKEY_KING_FOCUS_GOAL_SECONDS) {
                profile.gamificationStage = 'MONKEY_KING';
            }
            await profile.save();
        }

        sendSessionUpdateToUser(userId, session);
        (res as any).json(session);
    } catch (err) {
        next(err);
    }
};

// Start a break
export const startBreak = async (req: Request, res: Response, next: NextFunction) => {
    const authReq = req as AuthenticatedRequest;
    const userId = authReq.user!.id;
    const { type } = (req as any).body;

    if (!type) {
        return (res as any).status(400).json({ message: 'Break type is required.' });
    }

    try {
        const session = await StudySession.findOneAndUpdate(
            { user: userId, status: StudyStatus.Studying },
            {
                $set: { status: StudyStatus.Break, activeBreakType: type, lastActivity: new Date() },
                $push: { breakHistory: { startTime: new Date(), type } },
            },
            { new: true }
        );

        if (!session) {
            return (res as any).status(404).json({ message: 'No studying session found to start a break.' });
        }
        sendSessionUpdateToUser(userId, session);
        (res as any).json(session);
    } catch (error) {
        next(error);
    }
};

// Resume from a break
export const resumeFromBreak = async (req: Request, res: Response, next: NextFunction) => {
    const authReq = req as AuthenticatedRequest;
    const userId = authReq.user!.id;
    try {
        const session = await StudySession.findOneAndUpdate(
            { user: authReq.user!.id, status: StudyStatus.Break },
            {
                $set: {
                    status: StudyStatus.Studying,
                    activeBreakType: undefined,
                    'breakHistory.$[elem].endTime': new Date(),
                    lastActivity: new Date(),
                },
            },
            {
                arrayFilters: [{ 'elem.endTime': { $exists: false } }],
                new: true,
            }
        );

        if (!session) {
            return (res as any).status(404).json({ message: 'No session on break found to resume.' });
        }
        sendSessionUpdateToUser(userId, session);
        (res as any).json(session);
    } catch (error) {
        next(error);
    }
};


// Get the current or most recent session
export const getCurrentSession = async (req: Request, res: Response, next: NextFunction) => {
    const authReq = req as AuthenticatedRequest;
    try {
        const session = await StudySession.findOne({ user: authReq.user!.id }).sort({ createdAt: -1 });
        (res as any).json(session); // Will return null if no sessions exist
    } catch (error) {
        next(error);
    }
};
