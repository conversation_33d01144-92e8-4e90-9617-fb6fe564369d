"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const childProfileSchema = new mongoose_1.Schema({
    user: {
        type: mongoose_1.Schema.Types.ObjectId,
        required: true,
        ref: 'User'
    },
    nickname: { type: String, required: true },
    age: { type: Number, required: true },
    grade: { type: String, required: true },
    gender: { type: String, enum: ['boy', 'girl'], required: true },
    minSessionDuration: { type: Number, required: true },
    stretchBreak: { type: Number, required: true },
    waterBreak: { type: Number, required: true },
    restroomBreak: { type: Number, required: true },
    forcedBreakDuration: { type: Number, required: true },
    workDurationBeforeForcedBreak: { type: Number, required: true },
    waterBreakLimit: { type: Number, required: true },
    restroomBreakLimit: { type: Number, required: true },
    gamificationStage: {
        type: String,
        enum: ['STONE_MONKEY', 'MONKEY_KING'],
        default: 'STONE_MONKEY'
    },
    dailyFocusSeconds: {
        type: Number,
        default: 0
    },
    lastFocusUpdate: {
        type: Date,
        default: () => new Date()
    }
}, { timestamps: true });
const ChildProfile = mongoose_1.default.model('ChildProfile', childProfileSchema);
exports.default = ChildProfile;
