
import { Request, Response, NextFunction } from 'express';
import ChildProfile from '../models/childProfile.model';
import { AuthenticatedRequest } from '../types';

export const getChildProfile = async (req: Request, res: Response, next: NextFunction) => {
  const authReq = req as AuthenticatedRequest;
  try {
    const profile = await ChildProfile.findOne({ user: authReq.user!.id });
    if (!profile) {
      return (res as any).status(404).json({ message: 'Profile not found' });
    }
    (res as any).json(profile);
  } catch (error) {
    next(error);
  }
};

export const updateChildProfile = async (req: Request, res: Response, next: NextFunction) => {
  const authReq = req as AuthenticatedRequest;
  try {
    const profile = await ChildProfile.findOneAndUpdate({ user: authReq.user!.id }, (req as any).body, {
      new: true,
      runValidators: true,
    });
    if (!profile) {
      return (res as any).status(404).json({ message: 'Profile not found' });
    }
    (res as any).json(profile);
  } catch (error) {
    next(error);
  }
};
