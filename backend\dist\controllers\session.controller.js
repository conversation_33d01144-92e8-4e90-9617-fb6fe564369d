"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCurrentSession = exports.resumeFromBreak = exports.startBreak = exports.stopSession = exports.startSession = void 0;
const studySession_model_1 = __importStar(require("../models/studySession.model"));
const childProfile_model_1 = __importDefault(require("../models/childProfile.model"));
const realtime_service_1 = require("../services/realtime.service");
const report_service_1 = require("../services/report.service");
const MONKEY_KING_FOCUS_GOAL_SECONDS = 3600; // 1 hour
// Start a new study session
const startSession = async (req, res, next) => {
    const authReq = req;
    const userId = authReq.user.id;
    try {
        const existingSession = await studySession_model_1.default.findOne({
            user: authReq.user.id,
            status: { $in: [studySession_model_1.StudyStatus.Studying, studySession_model_1.StudyStatus.Break] },
        });
        if (existingSession) {
            return res.status(400).json({ message: 'An active session already exists.' });
        }
        // Reset daily focus seconds if it's a new day
        const profile = await childProfile_model_1.default.findOne({ user: userId });
        if (profile) {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            if (profile.lastFocusUpdate < today) {
                profile.dailyFocusSeconds = 0;
            }
            profile.lastFocusUpdate = new Date();
            await profile.save();
        }
        const now = new Date();
        const newSession = new studySession_model_1.default({
            user: userId,
            startTime: now,
            status: studySession_model_1.StudyStatus.Studying,
            lastActivity: now,
            currentRank: studySession_model_1.Rank.WUKONG, // Initialize rank
        });
        await newSession.save();
        (0, realtime_service_1.sendSessionUpdateToUser)(userId, newSession);
        res.status(201).json(newSession);
    }
    catch (error) {
        next(error);
    }
};
exports.startSession = startSession;
// Stop the current study session
const stopSession = async (req, res, next) => {
    const authReq = req;
    const userId = authReq.user.id;
    try {
        const session = await studySession_model_1.default.findOneAndUpdate({ user: userId, status: { $in: [studySession_model_1.StudyStatus.Studying, studySession_model_1.StudyStatus.Break] } }, {
            $set: {
                status: studySession_model_1.StudyStatus.Finished,
                endTime: new Date(),
                'breakHistory.$[elem].endTime': new Date(), // Close any open breaks
            },
        }, {
            arrayFilters: [{ 'elem.endTime': { $exists: false } }],
            new: true,
        });
        if (!session) {
            return res.status(404).json({ message: 'No active session found to stop.' });
        }
        // Update gamification progress
        const profile = await childProfile_model_1.default.findOne({ user: userId });
        if (profile) {
            const metrics = (0, report_service_1.calculateMetrics)([session]);
            profile.dailyFocusSeconds += metrics.focusedTime;
            profile.lastFocusUpdate = new Date();
            if (profile.gamificationStage === 'STONE_MONKEY' && profile.dailyFocusSeconds >= MONKEY_KING_FOCUS_GOAL_SECONDS) {
                profile.gamificationStage = 'MONKEY_KING';
            }
            await profile.save();
        }
        (0, realtime_service_1.sendSessionUpdateToUser)(userId, session);
        res.json(session);
    }
    catch (err) {
        next(err);
    }
};
exports.stopSession = stopSession;
// Start a break
const startBreak = async (req, res, next) => {
    const authReq = req;
    const userId = authReq.user.id;
    const { type } = req.body;
    if (!type) {
        return res.status(400).json({ message: 'Break type is required.' });
    }
    try {
        const session = await studySession_model_1.default.findOneAndUpdate({ user: userId, status: studySession_model_1.StudyStatus.Studying }, {
            $set: { status: studySession_model_1.StudyStatus.Break, activeBreakType: type, lastActivity: new Date() },
            $push: { breakHistory: { startTime: new Date(), type } },
        }, { new: true });
        if (!session) {
            return res.status(404).json({ message: 'No studying session found to start a break.' });
        }
        (0, realtime_service_1.sendSessionUpdateToUser)(userId, session);
        res.json(session);
    }
    catch (error) {
        next(error);
    }
};
exports.startBreak = startBreak;
// Resume from a break
const resumeFromBreak = async (req, res, next) => {
    const authReq = req;
    const userId = authReq.user.id;
    try {
        const session = await studySession_model_1.default.findOneAndUpdate({ user: authReq.user.id, status: studySession_model_1.StudyStatus.Break }, {
            $set: {
                status: studySession_model_1.StudyStatus.Studying,
                activeBreakType: undefined,
                'breakHistory.$[elem].endTime': new Date(),
                lastActivity: new Date(),
            },
        }, {
            arrayFilters: [{ 'elem.endTime': { $exists: false } }],
            new: true,
        });
        if (!session) {
            return res.status(404).json({ message: 'No session on break found to resume.' });
        }
        (0, realtime_service_1.sendSessionUpdateToUser)(userId, session);
        res.json(session);
    }
    catch (error) {
        next(error);
    }
};
exports.resumeFromBreak = resumeFromBreak;
// Get the current or most recent session
const getCurrentSession = async (req, res, next) => {
    const authReq = req;
    try {
        const session = await studySession_model_1.default.findOne({ user: authReq.user.id }).sort({ createdAt: -1 });
        res.json(session); // Will return null if no sessions exist
    }
    catch (error) {
        next(error);
    }
};
exports.getCurrentSession = getCurrentSession;
