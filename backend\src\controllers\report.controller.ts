
import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '../types';
import StudySession from '../models/studySession.model';
import { calculateMetrics } from '../services/report.service';

export const getReport = async (req: Request, res: Response, next: NextFunction) => {
  const authReq = req as AuthenticatedRequest;
  const { period } = (req as any).params; // 'daily', 'weekly', 'latest'
  const userId = authReq.user!.id;

  try {
    let startDate = new Date();
    let sessions;

    switch (period) {
      case 'daily':
        startDate.setHours(0, 0, 0, 0);
        sessions = await StudySession.find({ user: userId, startTime: { $gte: startDate } });
        break;
      case 'weekly':
        const day = startDate.getDay();
        const diff = startDate.getDate() - day + (day === 0 ? -6 : 1); // adjust when day is sunday
        startDate = new Date(startDate.setDate(diff));
        startDate.setHours(0, 0, 0, 0);
        sessions = await StudySession.find({ user: userId, startTime: { $gte: startDate } });
        break;
      case 'latest':
         sessions = await StudySession.find({ user: userId }).sort({ startTime: -1 }).limit(1);
         break;
      default:
        return (res as any).status(400).json({ message: 'Invalid report period.' });
    }

    const metrics = calculateMetrics(sessions);
    (res as any).json(metrics);

  } catch (error) {
    next(error);
  }
};
