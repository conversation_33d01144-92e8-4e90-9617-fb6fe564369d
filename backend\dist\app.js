"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = __importDefault(require("dotenv"));
const routes_1 = __importDefault(require("./routes"));
const error_middleware_1 = require("./middleware/error.middleware");
const config_1 = __importDefault(require("./config"));
dotenv_1.default.config();
const app = (0, express_1.default)();
// Middleware
app.use((0, cors_1.default)({
    origin: config_1.default.frontendUrl
}));
app.use(express_1.default.json({ limit: '10mb' })); // Increase limit for base64 images
app.use(express_1.default.urlencoded({ extended: true }));
// API Routes
app.get('/', (req, res) => {
    res.send('AI Homework Supervisor Backend is running.');
});
app.use('/api', routes_1.default);
// Error Handling
app.use(error_middleware_1.errorMiddleware);
exports.default = app;
