
import { Router } from 'express';
import authRoutes from './auth.routes';
import userRoutes from './user.routes';
import supervisionRoutes from './supervision.routes';
import subscriptionRoutes from './subscription.routes';
import reportRoutes from './report.routes';
import sessionRoutes from './session.routes';

const router = Router();

router.use('/auth', authRoutes);
router.use('/user', userRoutes);
router.use('/supervision', supervisionRoutes);
router.use('/subscription', subscriptionRoutes);
router.use('/reports', reportRoutes);
router.use('/sessions', sessionRoutes);

export default router;
