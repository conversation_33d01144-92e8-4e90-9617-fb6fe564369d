"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const user_controller_1 = require("../controllers/user.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const router = (0, express_1.Router)();
router.route('/profile').get(auth_middleware_1.protect, user_controller_1.getChildProfile).put(auth_middleware_1.protect, user_controller_1.updateChildProfile);
exports.default = router;
