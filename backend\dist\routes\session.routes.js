"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const session_controller_1 = require("../controllers/session.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const router = (0, express_1.Router)();
router.post('/start', auth_middleware_1.protect, session_controller_1.startSession);
router.post('/stop', auth_middleware_1.protect, session_controller_1.stopSession);
router.get('/current', auth_middleware_1.protect, session_controller_1.getCurrentSession);
router.post('/break', auth_middleware_1.protect, session_controller_1.startBreak);
router.post('/resume', auth_middleware_1.protect, session_controller_1.resumeFromBreak);
exports.default = router;
