"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserStatus = exports.loginUser = exports.registerUser = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const user_model_1 = __importDefault(require("../models/user.model"));
const childProfile_model_1 = __importDefault(require("../models/childProfile.model"));
const subscription_model_1 = __importDefault(require("../models/subscription.model"));
const config_1 = __importDefault(require("../config"));
const types_1 = require("../types");
const generateToken = (id) => {
    return jsonwebtoken_1.default.sign({ id }, config_1.default.jwtSecret, {
        expiresIn: '30d',
    });
};
const registerUser = async (req, res, next) => {
    const { phone, password } = req.body;
    if (!phone || !password || !/^\d{11}$/.test(phone)) {
        return res.status(400).json({ message: 'Please provide a valid 11-digit phone number and password.' });
    }
    try {
        const userExists = await user_model_1.default.findOne({ phone });
        if (userExists) {
            return res.status(400).json({ message: 'User already exists' });
        }
        const trialEndDate = new Date();
        trialEndDate.setDate(trialEndDate.getDate() + 5); // 5-day trial
        const user = new user_model_1.default({ phone, password, trialEndDate });
        // Create a default subscription entry
        const defaultSubscription = new subscription_model_1.default({
            user: user._id,
            plan: 'none',
            status: 'inactive',
            startDate: new Date(),
            endDate: trialEndDate,
        });
        // Create a default child profile
        const defaultProfile = new childProfile_model_1.default({
            user: user._id,
            ...types_1.DEFAULT_SETTINGS,
        });
        user.subscription = defaultSubscription._id;
        user.childProfile = defaultProfile._id;
        await defaultSubscription.save();
        await defaultProfile.save();
        await user.save();
        res.status(201).json({
            _id: user._id,
            phone: user.phone,
            token: generateToken(user._id.toString()),
        });
    }
    catch (error) {
        next(error);
    }
};
exports.registerUser = registerUser;
const loginUser = async (req, res, next) => {
    const { phone, password } = req.body;
    try {
        const user = await user_model_1.default.findOne({ phone });
        if (user && (await user.matchPassword(password))) {
            res.json({
                _id: user._id,
                phone: user.phone,
                token: generateToken(user._id.toString()),
            });
        }
        else {
            res.status(401).json({ message: 'Invalid phone number or password' });
        }
    }
    catch (error) {
        next(error);
    }
};
exports.loginUser = loginUser;
const getUserStatus = async (req, res, next) => {
    const authReq = req;
    try {
        const user = await user_model_1.default.findById(authReq.user.id).populate('subscription');
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        const sub = user.subscription;
        const now = new Date();
        const hasActiveSubscription = sub && sub.status === 'active' && sub.endDate && sub.endDate > now;
        const isTrialActive = user.trialEndDate && user.trialEndDate > now;
        let planName = '未订阅';
        let effectiveDailyLimit = 0; // in hours
        if (hasActiveSubscription) {
            const planDetails = types_1.PLAN_DETAILS[sub.plan];
            planName = `${planDetails.name}版`;
            effectiveDailyLimit = planDetails.dailyTimeLimit;
        }
        else if (isTrialActive) {
            planName = '免费体验版';
            effectiveDailyLimit = types_1.TRIAL_DAILY_LIMIT;
        }
        res.json({
            isAuthenticated: true,
            user: { phone: user.phone },
            isTrialActive,
            hasActiveSubscription,
            planName,
            effectiveDailyLimit,
            subscriptionEndDate: hasActiveSubscription ? sub.endDate : null,
            trialEndDate: user.trialEndDate,
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getUserStatus = getUserStatus;
