
import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '../types';
import { analyzeImageWithAI } from '../services/qwen.service';
import ChildProfile from '../models/childProfile.model';
import StudySession, { StudyStatus, Rank } from '../models/studySession.model';
import { sendSessionUpdateToUser } from '../services/realtime.service';

const calculateRank = (focusHistory: { isFocused: boolean, isOnSeat: boolean }[]): Rank => {
    const historySlice = focusHistory.slice(-5); // Analyze the last 5 data points
    if (historySlice.length < 3) return Rank.WUKONG; // Not enough data to judge

    const offSeatCount = historySlice.filter(h => !h.isOnSeat).length;
    const distractedCount = historySlice.filter(h => !h.isFocused && h.isOnSeat).length;

    if (offSeatCount >= 3) return Rank.BAILONGMA;
    if (offSeatCount >= 1) return Rank.SHASENG;
    if (distractedCount >= 3) return Rank.BAJIE;

    return Rank.WUKONG;
};

export const analyzeImage = async (req: Request, res: Response, next: NextFunction) => {
  const authReq = req as AuthenticatedRequest;
  const { base64Image } = (req as any).body;

  if (!base64Image) {
    return (res as any).status(400).json({ message: 'Image data is required.' });
  }

  try {
    const profile = await ChildProfile.findOne({ user: authReq.user!.id });
    if (!profile) {
        return (res as any).status(404).json({ message: 'Child profile not found.' });
    }
    
    // Find the active (studying or on break) study session
    const activeSession = await StudySession.findOne({ 
        user: authReq.user!.id, 
        status: { $in: [StudyStatus.Studying, StudyStatus.Break] }
    });

    if (!activeSession) {
        return (res as any).status(404).json({ message: 'No active session found. Cannot perform analysis.' });
    }

    // Update activity timestamp to prevent timeout
    activeSession.lastActivity = new Date();

    // Only record focus history and update rank if the child is supposed to be studying
    if (activeSession.status === StudyStatus.Studying) {
      const currentFocus = await analyzeImageWithAI(base64Image, profile, activeSession.currentRank);
      
      activeSession.focusHistory.push({
          timestamp: new Date(),
          isFocused: currentFocus.analysis.isFocused,
          isOnSeat: currentFocus.analysis.isOnSeat,
      });

      // Recalculate and update rank based on recent history
      const newRank = calculateRank(activeSession.focusHistory);
      activeSession.currentRank = newRank;
      
      await activeSession.save();
      
      // Get the final analysis message based on the *new* rank
      const finalResult = await analyzeImageWithAI(base64Image, profile, newRank, true);

      // Send an immediate session update over WebSocket with the new rank
      sendSessionUpdateToUser(authReq.user!.id, activeSession);

      (res as any).json(finalResult);

    } else { // If on break, just do a basic analysis to keep the session alive but don't record history
        const breakAnalysis = await analyzeImageWithAI(base64Image, profile, activeSession.currentRank);
        await activeSession.save();
        (res as any).json(breakAnalysis);
    }

  } catch (error) {
    next(error);
  }
};
