"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getReport = void 0;
const studySession_model_1 = __importDefault(require("../models/studySession.model"));
const report_service_1 = require("../services/report.service");
const getReport = async (req, res, next) => {
    const authReq = req;
    const { period } = req.params; // 'daily', 'weekly', 'latest'
    const userId = authReq.user.id;
    try {
        let startDate = new Date();
        let sessions;
        switch (period) {
            case 'daily':
                startDate.setHours(0, 0, 0, 0);
                sessions = await studySession_model_1.default.find({ user: userId, startTime: { $gte: startDate } });
                break;
            case 'weekly':
                const day = startDate.getDay();
                const diff = startDate.getDate() - day + (day === 0 ? -6 : 1); // adjust when day is sunday
                startDate = new Date(startDate.setDate(diff));
                startDate.setHours(0, 0, 0, 0);
                sessions = await studySession_model_1.default.find({ user: userId, startTime: { $gte: startDate } });
                break;
            case 'latest':
                sessions = await studySession_model_1.default.find({ user: userId }).sort({ startTime: -1 }).limit(1);
                break;
            default:
                return res.status(400).json({ message: 'Invalid report period.' });
        }
        const metrics = (0, report_service_1.calculateMetrics)(sessions);
        res.json(metrics);
    }
    catch (error) {
        next(error);
    }
};
exports.getReport = getReport;
