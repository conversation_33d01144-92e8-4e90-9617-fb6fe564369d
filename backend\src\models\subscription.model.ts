
import mongoose, { Schema, Document, Types } from 'mongoose';
import { SubscriptionPlan } from '../types';

export interface ISubscription extends Document {
  user: Types.ObjectId;
  plan: SubscriptionPlan;
  status: 'active' | 'inactive' | 'cancelled';
  startDate: Date;
  endDate: Date;
  alipayTradeNo?: string;
}

const subscriptionSchema = new Schema<ISubscription>({
  user: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'User',
  },
  plan: {
    type: String,
    enum: ['none', 'standard', 'pro'],
    required: true,
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'cancelled'],
    required: true,
  },
  startDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  alipayTradeNo: { type: String },
}, { timestamps: true });

const Subscription = mongoose.model<ISubscription>('Subscription', subscriptionSchema);

export default Subscription;
