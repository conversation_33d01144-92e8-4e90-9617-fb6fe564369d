
import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import config from '../config';
import { AuthenticatedRequest } from '../types';

export const protect = (req: Request, res: Response, next: NextFunction) => {
  let token;
  const authReq = req as AuthenticatedRequest;

  if ((req as any).headers.authorization && (req as any).headers.authorization.startsWith('Bearer')) {
    try {
      token = (req as any).headers.authorization.split(' ')[1];
      const decoded = jwt.verify(token, config.jwtSecret) as { id: string };
      authReq.user = { id: decoded.id };
      next();
    } catch (error) {
      console.error(error);
      (res as any).status(401).json({ message: 'Not authorized, token failed' });
    }
  }

  if (!token) {
    (res as any).status(401).json({ message: 'Not authorized, no token' });
  }
};
