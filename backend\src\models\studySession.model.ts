
import mongoose, { Schema, Document, Types } from 'mongoose';

export enum StudyStatus {
  Idle = 'IDLE',
  Studying = 'STUDYING',
  Paused = 'PAUSED', // Paused is now managed by break status
  Break = 'BREAK',
  Finished = 'FINISHED',
}

export enum BreakType {
  Stretch = 'STRETCH',
  Water = 'WATER',
  Restroom = 'RESTROOM',
  Forced = 'FORCED',
}

export enum Rank {
    WUKONG = 'WUKONG',
    BAJIE = 'BAJIE',
    SHASENG = 'SHASENG',
    BAILONGMA = 'BAILONGMA',
}

interface FocusEntry {
  timestamp: Date;
  isFocused: boolean;
  isOnSeat: boolean;
}

interface BreakEntry {
  startTime: Date;
  endTime?: Date;
  type: BreakType;
}

export interface IStudySession extends Document {
  user: Types.ObjectId;
  startTime: Date;
  endTime?: Date;
  status: StudyStatus;
  activeBreakType?: BreakType;
  focusHistory: FocusEntry[];
  breakHistory: BreakEntry[];
  lastActivity?: Date;
  currentRank: Rank;
}

const studySessionSchema = new Schema<IStudySession>({
  user: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'User',
  },
  startTime: { type: Date, required: true },
  endTime: { type: Date },
  status: {
    type: String,
    enum: Object.values(StudyStatus),
    required: true,
    default: StudyStatus.Studying,
  },
  activeBreakType: {
    type: String,
    enum: Object.values(BreakType),
  },
  focusHistory: [{
    _id: false,
    timestamp: Date,
    isFocused: Boolean,
    isOnSeat: Boolean,
  }],
  breakHistory: [{
    _id: false,
    startTime: Date,
    endTime: Date,
    type: { type: String, enum: Object.values(BreakType) },
  }],
  lastActivity: { type: Date },
  currentRank: {
      type: String,
      enum: Object.values(Rank),
      default: Rank.WUKONG,
  }
}, { timestamps: true });

const StudySession = mongoose.model<IStudySession>('StudySession', studySessionSchema);

export default StudySession;
