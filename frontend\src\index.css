@tailwind base;
@tailwind components;
@tailwind utilities;

/* Add any custom base styles here */
body {
    font-family: 'Inter', sans-serif;
}

/* Auth screen background */
.auth-bg {
    background: linear-gradient(170deg, #0f172a, #1e293b 60%, #334155);
    overflow: hidden;
    position: relative;
}

.auth-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(ellipse at 70% 30%, rgba(14, 165, 233, 0.15) 0%, transparent 50%),
        radial-gradient(ellipse at 30% 70%, rgba(79, 70, 229, 0.15) 0%, transparent 50%);
    animation: bgPan 20s linear infinite;
    background-size: 200% 200%;
}


/* Gamification Theme for Monkey King */
@layer utilities {
    .theme-water-curtain-cave {
        @apply border-4 border-transparent rounded-2xl p-1;
        /* The 'background-clip' property is key here. It clips the first background to the content area, and the second to the border area. */
        background-image:
            linear-gradient(rgb(248 250 252), rgb(248 250 252)),
            linear-gradient(135deg, rgb(34 211 238), rgb(59 130 246), rgb(147 51 234));
        background-origin: border-box;
        background-clip: padding-box, border-box;
        animation: water-flow 5s ease infinite;
        background-size: 200% 200%; /* For animation */
    }

    .dark .theme-water-curtain-cave {
        /* Only update the background-image for dark mode, keeping other properties. */
        background-image:
            linear-gradient(rgb(30 41 59), rgb(30 41 59)),
            linear-gradient(135deg, rgb(34 211 238), rgb(59 130 246), rgb(147 51 234));
    }
}

@keyframes bgPan {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes water-flow {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}
