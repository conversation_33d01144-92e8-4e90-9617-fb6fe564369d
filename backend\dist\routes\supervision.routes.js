"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const supervision_controller_1 = require("../controllers/supervision.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const router = (0, express_1.Router)();
router.post('/analyze', auth_middleware_1.protect, supervision_controller_1.analyzeImage);
// Session management routes (start, stop, break) can be added here
// For simplicity, we can infer start/stop from the first/last analysis call of a day
// or create explicit endpoints.
exports.default = router;
