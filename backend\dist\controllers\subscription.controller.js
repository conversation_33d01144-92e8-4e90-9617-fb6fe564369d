"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleAlipayWebhook = exports.createSubscriptionOrder = void 0;
const alipay_service_1 = require("../services/alipay.service");
const subscription_model_1 = __importDefault(require("../models/subscription.model"));
const user_model_1 = __importDefault(require("../models/user.model"));
const createSubscriptionOrder = async (req, res, next) => {
    const authReq = req;
    const { plan } = req.body;
    const userId = authReq.user.id;
    if (!plan || !['standard', 'pro'].includes(plan)) {
        return res.status(400).json({ message: 'Invalid subscription plan.' });
    }
    try {
        const { paymentUrl, outTradeNo } = await (0, alipay_service_1.generateAlipayUrl)(userId, plan);
        // Here you could create a pending subscription record in your DB
        // with the outTradeNo for later verification.
        res.json({ paymentUrl, orderId: outTradeNo });
    }
    catch (error) {
        next(error);
    }
};
exports.createSubscriptionOrder = createSubscriptionOrder;
const handleAlipayWebhook = async (req, res, next) => {
    console.log('Received Alipay webhook:', req.body);
    const params = req.body;
    // For a real implementation, add robust validation here.
    // This is a simplified mock.
    const isVerified = (0, alipay_service_1.verifyAlipaySign)(params);
    if (isVerified && params.trade_status === 'TRADE_SUCCESS') {
        const outTradeNo = params.out_trade_no; // Your internal order ID
        const alipayTradeNo = params.trade_no; // Alipay's transaction ID
        // The out_trade_no should contain info to identify the user and plan.
        // e.g., 'sub-standard-USERID-TIMESTAMP'
        const [type, plan, userId] = outTradeNo.split('-');
        if (type !== 'sub' || !userId) {
            return res.status(400).send('failure_invalid_order');
        }
        try {
            const user = await user_model_1.default.findById(userId);
            if (!user)
                return res.status(404).send('failure_user_not_found');
            const now = new Date();
            const endDate = new Date(now.setMonth(now.getMonth() + 1));
            // Find existing subscription and update it, or create a new one
            await subscription_model_1.default.findOneAndUpdate({ user: userId }, {
                plan: plan,
                status: 'active',
                startDate: new Date(),
                endDate: endDate,
                alipayTradeNo: alipayTradeNo,
            }, { upsert: true, new: true });
            console.log(`Successfully activated subscription for user ${userId}`);
            res.status(200).send('success');
        }
        catch (error) {
            console.error('Webhook processing error:', error);
            res.status(500).send('failure_server_error');
        }
    }
    else {
        console.log('Alipay webhook verification failed or trade not successful.');
        res.status(400).send('failure');
    }
};
exports.handleAlipayWebhook = handleAlipayWebhook;
