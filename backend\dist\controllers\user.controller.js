"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateChildProfile = exports.getChildProfile = void 0;
const childProfile_model_1 = __importDefault(require("../models/childProfile.model"));
const getChildProfile = async (req, res, next) => {
    const authReq = req;
    try {
        const profile = await childProfile_model_1.default.findOne({ user: authReq.user.id });
        if (!profile) {
            return res.status(404).json({ message: 'Profile not found' });
        }
        res.json(profile);
    }
    catch (error) {
        next(error);
    }
};
exports.getChildProfile = getChildProfile;
const updateChildProfile = async (req, res, next) => {
    const authReq = req;
    try {
        const profile = await childProfile_model_1.default.findOneAndUpdate({ user: authReq.user.id }, req.body, {
            new: true,
            runValidators: true,
        });
        if (!profile) {
            return res.status(404).json({ message: 'Profile not found' });
        }
        res.json(profile);
    }
    catch (error) {
        next(error);
    }
};
exports.updateChildProfile = updateChildProfile;
