
import { Request } from 'express';

// Extend Express Request type to include user from JWT
export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
  };
}

export type SubscriptionPlan = 'none' | 'standard' | 'pro';

export const PLAN_DETAILS: Record<Exclude<SubscriptionPlan, 'none'>, { name: string; price: number; dailyTimeLimit: number }> = {
  standard: {
    name: '标准版',
    price: 19.9,
    dailyTimeLimit: 3,
  },
  pro: {
    name: '专业版',
    price: 29.9,
    dailyTimeLimit: 5,
  },
};

export const TRIAL_DAILY_LIMIT = 1; // 1 hour for trial

export const DEFAULT_SETTINGS = {
  nickname: '宝贝',
  age: 8,
  grade: '小学二年级',
  gender: 'boy',
  minSessionDuration: 5,
  stretchBreak: 5,
  waterBreak: 2,
  restroomBreak: 5,
  workDurationBeforeForcedBreak: 45,
  forcedBreakDuration: 10,
  waterBreakLimit: 4,
  restroomBreakLimit: 2,
};
