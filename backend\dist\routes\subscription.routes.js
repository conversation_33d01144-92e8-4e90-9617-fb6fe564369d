"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const subscription_controller_1 = require("../controllers/subscription.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const router = (0, express_1.Router)();
router.post('/create-order', auth_middleware_1.protect, subscription_controller_1.createSubscriptionOrder);
router.post('/webhook/alipay', subscription_controller_1.handleAlipayWebhook); // This webhook is open
exports.default = router;
