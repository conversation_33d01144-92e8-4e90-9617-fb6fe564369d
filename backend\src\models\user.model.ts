
import mongoose, { Schema, Document, Types } from 'mongoose';
import bcrypt from 'bcryptjs';

export interface IUser extends Document {
  phone: string;
  password: string;
  trialEndDate?: Date;
  subscription: Types.ObjectId;
  childProfile: Types.ObjectId;
  matchPassword(enteredPassword: string): Promise<boolean>;
}

const userSchema = new Schema<IUser>({
  phone: {
    type: String,
    required: true,
    unique: true,
    match: /^\d{11}$/,
  },
  password: {
    type: String,
    required: true,
  },
  trialEndDate: {
    type: Date,
    required: true,
  },
  subscription: {
    type: Schema.Types.ObjectId,
    ref: 'Subscription',
  },
  childProfile: {
      type: Schema.Types.ObjectId,
      ref: 'ChildProfile'
  }
}, { timestamps: true });

// Password hashing middleware
userSchema.pre('save', async function (next) {
  if (!this.isModified('password')) {
    next();
  }
  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password!, salt);
});

// Password comparison method
userSchema.methods.matchPassword = async function (enteredPassword: string): Promise<boolean> {
  return await bcrypt.compare(enteredPassword, this.password!);
};

const User = mongoose.model<IUser>('User', userSchema);

export default User;