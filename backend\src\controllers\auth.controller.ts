
import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import User from '../models/user.model';
import ChildProfile from '../models/childProfile.model';
import Subscription from '../models/subscription.model';
import config from '../config';
import { AuthenticatedRequest, DEFAULT_SETTINGS, PLAN_DETAILS, TRIAL_DAILY_LIMIT, SubscriptionPlan } from '../types';

const generateToken = (id: string) => {
  return jwt.sign({ id }, config.jwtSecret, {
    expiresIn: '30d',
  });
};

export const registerUser = async (req: Request, res: Response, next: NextFunction) => {
  const { phone, password } = (req as any).body;

  if (!phone || !password || !/^\d{11}$/.test(phone)) {
    return (res as any).status(400).json({ message: 'Please provide a valid 11-digit phone number and password.' });
  }

  try {
    const userExists = await User.findOne({ phone });
    if (userExists) {
      return (res as any).status(400).json({ message: 'User already exists' });
    }

    const trialEndDate = new Date();
    trialEndDate.setDate(trialEndDate.getDate() + 5); // 5-day trial

    const user = new User({ phone, password, trialEndDate });

    // Create a default subscription entry
    const defaultSubscription = new Subscription({
        user: user._id,
        plan: 'none',
        status: 'inactive',
        startDate: new Date(),
        endDate: trialEndDate,
    });
    
    // Create a default child profile
    const defaultProfile = new ChildProfile({
        user: user._id,
        ...DEFAULT_SETTINGS,
    });
    
    user.subscription = defaultSubscription._id as any;
    user.childProfile = defaultProfile._id as any;
    
    await defaultSubscription.save();
    await defaultProfile.save();
    await user.save();

    (res as any).status(201).json({
      _id: user._id,
      phone: user.phone,
      token: generateToken(user._id!.toString()),
    });
  } catch (error) {
    next(error);
  }
};

export const loginUser = async (req: Request, res: Response, next: NextFunction) => {
  const { phone, password } = (req as any).body;
  try {
    const user = await User.findOne({ phone });

    if (user && (await user.matchPassword(password))) {
      (res as any).json({
        _id: user._id,
        phone: user.phone,
        token: generateToken(user._id!.toString()),
      });
    } else {
      (res as any).status(401).json({ message: 'Invalid phone number or password' });
    }
  } catch (error) {
    next(error);
  }
};

export const getUserStatus = async (req: Request, res: Response, next: NextFunction) => {
    const authReq = req as AuthenticatedRequest;
    try {
        const user = await User.findById(authReq.user!.id).populate('subscription');
        if (!user) {
            return (res as any).status(404).json({ message: 'User not found' });
        }

        const sub = user.subscription as any;
        const now = new Date();
        const hasActiveSubscription = sub && sub.status === 'active' && sub.endDate && sub.endDate > now;
        const isTrialActive = user.trialEndDate && user.trialEndDate > now;

        let planName = '未订阅';
        let effectiveDailyLimit = 0; // in hours

        if (hasActiveSubscription) {
            const planDetails = PLAN_DETAILS[sub.plan as Exclude<SubscriptionPlan, 'none'>];
            planName = `${planDetails.name}版`;
            effectiveDailyLimit = planDetails.dailyTimeLimit;
        } else if (isTrialActive) {
            planName = '免费体验版';
            effectiveDailyLimit = TRIAL_DAILY_LIMIT;
        }
        
        (res as any).json({
            isAuthenticated: true,
            user: { phone: user.phone },
            isTrialActive,
            hasActiveSubscription,
            planName,
            effectiveDailyLimit,
            subscriptionEndDate: hasActiveSubscription ? sub.endDate : null,
            trialEndDate: user.trialEndDate,
        });

    } catch (error) {
        next(error);
    }
};
