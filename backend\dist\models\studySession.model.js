"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Rank = exports.BreakType = exports.StudyStatus = void 0;
const mongoose_1 = __importStar(require("mongoose"));
var StudyStatus;
(function (StudyStatus) {
    StudyStatus["Idle"] = "IDLE";
    StudyStatus["Studying"] = "STUDYING";
    StudyStatus["Paused"] = "PAUSED";
    StudyStatus["Break"] = "BREAK";
    StudyStatus["Finished"] = "FINISHED";
})(StudyStatus || (exports.StudyStatus = StudyStatus = {}));
var BreakType;
(function (BreakType) {
    BreakType["Stretch"] = "STRETCH";
    BreakType["Water"] = "WATER";
    BreakType["Restroom"] = "RESTROOM";
    BreakType["Forced"] = "FORCED";
})(BreakType || (exports.BreakType = BreakType = {}));
var Rank;
(function (Rank) {
    Rank["WUKONG"] = "WUKONG";
    Rank["BAJIE"] = "BAJIE";
    Rank["SHASENG"] = "SHASENG";
    Rank["BAILONGMA"] = "BAILONGMA";
})(Rank || (exports.Rank = Rank = {}));
const studySessionSchema = new mongoose_1.Schema({
    user: {
        type: mongoose_1.Schema.Types.ObjectId,
        required: true,
        ref: 'User',
    },
    startTime: { type: Date, required: true },
    endTime: { type: Date },
    status: {
        type: String,
        enum: Object.values(StudyStatus),
        required: true,
        default: StudyStatus.Studying,
    },
    activeBreakType: {
        type: String,
        enum: Object.values(BreakType),
    },
    focusHistory: [{
            _id: false,
            timestamp: Date,
            isFocused: Boolean,
            isOnSeat: Boolean,
        }],
    breakHistory: [{
            _id: false,
            startTime: Date,
            endTime: Date,
            type: { type: String, enum: Object.values(BreakType) },
        }],
    lastActivity: { type: Date },
    currentRank: {
        type: String,
        enum: Object.values(Rank),
        default: Rank.WUKONG,
    }
}, { timestamps: true });
const StudySession = mongoose_1.default.model('StudySession', studySessionSchema);
exports.default = StudySession;
