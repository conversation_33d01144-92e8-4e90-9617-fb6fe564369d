import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IChildProfile extends Document {
  user: Types.ObjectId;
  nickname: string;
  age: number;
  grade: string;
  gender: 'boy' | 'girl';
  minSessionDuration: number;
  stretchBreak: number;
  waterBreak: number;
  restroomBreak: number;
  forcedBreakDuration: number;
  workDurationBeforeForcedBreak: number;
  waterBreakLimit: number;
  restroomBreakLimit: number;
  gamificationStage: 'STONE_MONKEY' | 'MONKEY_KING';
  dailyFocusSeconds: number;
  lastFocusUpdate: Date;
}

const childProfileSchema = new Schema<IChildProfile>({
  user: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'User'
  },
  nickname: { type: String, required: true },
  age: { type: Number, required: true },
  grade: { type: String, required: true },
  gender: { type: String, enum: ['boy', 'girl'], required: true },
  minSessionDuration: { type: Number, required: true },
  stretchBreak: { type: Number, required: true },
  waterBreak: { type: Number, required: true },
  restroomBreak: { type: Number, required: true },
  forcedBreakDuration: { type: Number, required: true },
  workDurationBeforeForcedBreak: { type: Number, required: true },
  waterBreakLimit: { type: Number, required: true },
  restroomBreakLimit: { type: Number, required: true },
  gamificationStage: {
    type: String,
    enum: ['STONE_MONKEY', 'MONKEY_KING'],
    default: 'STONE_MONKEY'
  },
  dailyFocusSeconds: {
    type: Number,
    default: 0
  },
  lastFocusUpdate: {
    type: Date,
    default: () => new Date()
  }
}, { timestamps: true });

const ChildProfile = mongoose.model<IChildProfile>('ChildProfile', childProfileSchema);

export default ChildProfile;