
import dotenv from 'dotenv';

dotenv.config();

const config = {
    port: 5000,
    mongoURI: process.env.MONGO_URI!,
    jwtSecret: process.env.JWT_SECRET!,
    frontendUrl: 'http://localhost:3000',
    qwenApiKey: process.env.QWEN_API_KEY!,
    qwenApiBaseUrl: process.env.QWEN_API_BASE_URL!,
    alipay: {
        appId: process.env.ALIPAY_APP_ID!,
        gatewayUrl: process.env.ALIPAY_GATEWAY_URL!,
    }
};

// Validate that all required environment variables are set
if (!config.mongoURI || !config.jwtSecret || !config.frontendUrl || !config.qwenApiKey || !config.qwenApiBaseUrl || !config.alipay.appId || !config.alipay.gatewayUrl) {
    console.error("FATAL ERROR: Required environment variables are not set.");
    (process as any).exit(1);
}


export default config;